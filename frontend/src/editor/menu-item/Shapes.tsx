"use client";
import {
  Box,
  Grid,
  Typography,
  Paper,
  useTheme,
  Tooltip,
  Tabs,
  Tab,
  Collapse,
} from "@mui/material";
import { observer } from "mobx-react";
import React, { useState, useEffect } from "react";
import { StoreContext } from "../../store";
import { ShapeType } from "../../types";

// 定义形状分类
const SHAPE_CATEGORIES = {
  basic: "基础图形",
  polygons: "多边形",
};

// 定义形状资源
const SHAPE_RESOURCES: {
  name: string;
  type: ShapeType;
  category: keyof typeof SHAPE_CATEGORIES;
  description?: string;
  icon: React.ReactNode;
}[] = [
  {
    name: "矩形",
    type: "rect",
    category: "basic",
    description: "绘制一个矩形",
    icon: (
      <Box
        sx={{
          width: 40,
          height: 30,
          bgcolor: "#F44336", // 红色
          borderRadius: 1,
        }}
      />
    ),
  },
  {
    name: "圆角矩形",
    type: "roundedRect",
    category: "basic",
    description: "绘制一个圆角矩形",
    icon: (
      <Box
        sx={{
          width: 40,
          height: 40,
          bgcolor: "#2196F3", // 蓝色
        }}
      />
    ),
  },
  {
    name: "圆形",
    type: "circle",
    category: "basic",
    description: "绘制一个圆形",
    icon: (
      <Box
        sx={{
          width: 40,
          height: 40,
          bgcolor: "#FFC107", // 黄色
          borderRadius: "50%",
        }}
      />
    ),
  },
  {
    name: "椭圆",
    type: "ellipse",
    category: "basic",
    description: "绘制一个椭圆形",
    icon: (
      <Box
        sx={{
          width: 45,
          height: 30,
          bgcolor: "#4CAF50", // 绿色
          borderRadius: "50%",
        }}
      />
    ),
  },
  {
    name: "三角形",
    type: "triangle",
    category: "basic",
    description: "绘制一个三角形",
    icon: (
      <Box
        sx={{
          width: 0,
          height: 0,
          borderLeft: "18px solid transparent",
          borderRight: "18px solid transparent",
          borderBottom: "32px solid #9C27B0", // 紫色
        }}
      />
    ),
  },
  {
    name: "直线",
    type: "line",
    category: "basic",
    description: "绘制一条直线",
    icon: (
      <Box
        sx={{
          width: 50,
          height: 4,
          bgcolor: "#FF9800", // 橙色
        }}
      />
    ),
  },
  {
    name: "菱形",
    type: "diamond",
    category: "basic",
    description: "绘制一个菱形",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 28,
            height: 28,
            bgcolor: "#2196F3", // 蓝色
            transform: "rotate(45deg)",
          }}
        />
      </Box>
    ),
  },
  {
    name: "五边形",
    type: "pentagon",
    category: "basic",
    description: "绘制一个五边形",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 40,
            bgcolor: "#F44336", // 红色
            clipPath: "polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%)",
          }}
        />
      </Box>
    ),
  },
  {
    name: "六边形",
    type: "hexagon",
    category: "basic",
    description: "绘制一个六边形",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 40,
            bgcolor: "#2196F3", // 蓝色
            clipPath:
              "polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)",
          }}
        />
      </Box>
    ),
  },
  {
    name: "八边形",
    type: "octagon",
    category: "basic",
    description: "绘制一个八边形",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 40,
            bgcolor: "#FFC107", // 黄色
            clipPath:
              "polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%)",
          }}
        />
      </Box>
    ),
  },
  {
    name: "右箭头",
    type: "rightArrow",
    category: "basic",
    description: "绘制一个右箭头",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 0,
            height: 0,
            borderTop: "8px solid transparent",
            borderBottom: "8px solid transparent",
            borderLeft: "20px solid #2196F3", // 蓝色
            position: "relative",
            "&::before": {
              content: '""',
              position: "absolute",
              left: "-30px",
              top: "-4px",
              width: "20px",
              height: "8px",
              bgcolor: "#2196F3",
            },
          }}
        />
      </Box>
    ),
  },
  {
    name: "上箭头",
    type: "upArrow",
    category: "basic",
    description: "绘制一个上箭头",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 0,
            height: 0,
            borderLeft: "8px solid transparent",
            borderRight: "8px solid transparent",
            borderBottom: "20px solid #F44336", // 红色
            position: "relative",
            "&::after": {
              content: '""',
              position: "absolute",
              top: "20px",
              left: "-4px",
              width: "8px",
              height: "15px",
              bgcolor: "#F44336",
            },
          }}
        />
      </Box>
    ),
  },

  {
    name: "叉号",
    type: "cross",
    category: "basic",
    description: "绘制一个叉号",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: "25px",
            height: "25px",
            position: "relative",
            "&::before, &::after": {
              content: '""',
              position: "absolute",
              left: "11px",
              top: "2px",
              width: "3px",
              height: "20px",
              bgcolor: "#FFC107", // 黄色
              borderRadius: "2px",
            },
            "&::before": {
              transform: "rotate(45deg)",
            },
            "&::after": {
              transform: "rotate(-45deg)",
            },
          }}
        />
      </Box>
    ),
  },
  {
    name: "下箭头",
    type: "downArrow",
    category: "basic",
    description: "绘制一个下箭头",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 0,
            height: 0,
            borderLeft: "8px solid transparent",
            borderRight: "8px solid transparent",
            borderTop: "20px solid #9C27B0", // 紫色
            position: "relative",
            "&::before": {
              content: '""',
              position: "absolute",
              top: "-35px",
              left: "-4px",
              width: "8px",
              height: "15px",
              bgcolor: "#9C27B0",
            },
          }}
        />
      </Box>
    ),
  },

  {
    name: "星形",
    type: "star",
    category: "basic",
    description: "绘制一个五角星",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 40,
            bgcolor: "#FFC107", // 黄色
            clipPath:
              "polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)",
          }}
        />
      </Box>
    ),
  },
  {
    name: "四角星",
    type: "fourPointStar",
    category: "basic",
    description: "绘制一个四角星",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 30,
            height: 30,
            bgcolor: "#2196F3", // 蓝色
            clipPath:
              "polygon(50% 0%, 70% 30%, 100% 20%, 80% 50%, 100% 80%, 70% 70%, 50% 100%, 30% 70%, 0% 80%, 20% 50%, 0% 20%, 30% 30%)",
          }}
        />
      </Box>
    ),
  },
  {
    name: "六角星",
    type: "sixPointStar",
    category: "basic",
    description: "绘制一个六角星",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 32,
            height: 32,
            bgcolor: "#F44336", // 红色
            clipPath:
              "polygon(50% 0%, 65% 30%, 100% 25%, 75% 50%, 100% 75%, 65% 70%, 50% 100%, 35% 70%, 0% 75%, 25% 50%, 0% 25%, 35% 30%)",
          }}
        />
      </Box>
    ),
  },
  {
    name: "八角星",
    type: "eightPointStar",
    category: "basic",
    description: "绘制一个八角星",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 30,
            height: 30,
            bgcolor: "#FF9800", // 橙色
            clipPath:
              "polygon(50% 0%, 65% 25%, 90% 15%, 80% 40%, 100% 50%, 80% 60%, 90% 85%, 65% 75%, 50% 100%, 35% 75%, 10% 85%, 20% 60%, 0% 50%, 20% 40%, 10% 15%, 35% 25%)",
          }}
        />
      </Box>
    ),
  },
  {
    name: "太阳形",
    type: "sunBurst",
    category: "basic",
    description: "绘制一个太阳形状",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 28,
            height: 28,
            bgcolor: "#FFC107", // 黄色
            borderRadius: "50%",
            position: "relative",
            "&::before": {
              content: '""',
              position: "absolute",
              top: "-6px",
              left: "-6px",
              width: "40px",
              height: "40px",
              bgcolor: "#FFC107",
              clipPath:
                "polygon(50% 0%, 55% 25%, 75% 15%, 65% 35%, 85% 35%, 65% 50%, 85% 65%, 65% 65%, 75% 85%, 55% 75%, 50% 100%, 45% 75%, 25% 85%, 35% 65%, 15% 65%, 35% 50%, 15% 35%, 35% 35%, 25% 15%, 45% 25%)",
              borderRadius: 0,
            },
          }}
        />
      </Box>
    ),
  },
  {
    name: "半圆",
    type: "semicircle",
    category: "basic",
    description: "绘制一个半圆",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 35,
            height: 18,
            bgcolor: "#9C27B0", // 紫色
            borderTopLeftRadius: "35px",
            borderTopRightRadius: "35px",
          }}
        />
      </Box>
    ),
  },
  {
    name: "四分之一圆",
    type: "quarterCircle",
    category: "basic",
    description: "绘制一个四分之一圆",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "flex-end",
          justifyContent: "flex-start",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 25,
            height: 25,
            bgcolor: "#2196F3", // 蓝色
            borderBottomLeftRadius: "25px",
          }}
        />
      </Box>
    ),
  },
  {
    name: "环形",
    type: "ring",
    category: "basic",
    description: "绘制一个环形",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 30,
            height: 30,
            bgcolor: "#4CAF50", // 绿色
            borderRadius: "50%",
            position: "relative",
            "&::after": {
              content: '""',
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              width: "15px",
              height: "15px",
              bgcolor: "#f5f5f5",
              borderRadius: "50%",
            },
          }}
        />
      </Box>
    ),
  },
  {
    name: "半环",
    type: "halfRing",
    category: "basic",
    description: "绘制一个半环",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 28,
            height: 14,
            border: "4px solid #FF9800", // 橙色
            borderBottom: "none",
            borderTopLeftRadius: "28px",
            borderTopRightRadius: "28px",
          }}
        />
      </Box>
    ),
  },
  {
    name: "加号",
    type: "plus",
    category: "basic",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: "20px",
            height: "20px",
            position: "relative",
            "&::before, &::after": {
              content: '""',
              position: "absolute",
              bgcolor: "#2196F3", // 蓝色
            },
            "&::before": {
              left: "50%",
              top: 0,
              transform: "translateX(-50%)",
              width: "4px",
              height: "20px",
            },
            "&::after": {
              top: "50%",
              left: 0,
              transform: "translateY(-50%)",
              width: "20px",
              height: "4px",
            },
          }}
        />
      </Box>
    ),
  },
  {
    name: "拱形",
    type: "arch",
    category: "basic",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 40,
            bgcolor: "#673AB7", // 深紫色，替换原来的grey.400
            borderTopLeftRadius: "100%",
            borderTopRightRadius: "100%",
            position: "relative",
            overflow: "hidden",
          }}
        >
          <Box
            sx={{
              position: "absolute",
              bottom: 0,
              left: 0,
              width: "100%",
              height: "50%",
              bgcolor: "#673AB7", // 深紫色，替换原来的grey.400
            }}
          />
        </Box>
      </Box>
    ),
  },
  {
    name: "平行四边形",
    type: "parallelogram",
    category: "basic",
    description: "绘制一个平行四边形",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 30,
            bgcolor: "#009688", // 蓝绿色，替换原来的grey.400
            clipPath: "polygon(25% 0%, 100% 0%, 75% 100%, 0% 100%)",
          }}
        />
      </Box>
    ),
  },
];

// 形状项组件
const ShapeItem = ({ shape, onClick, selected }) => {
  const theme = useTheme();
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Paper
      elevation={0}
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        p: 1.5,
        cursor: "pointer",
        transition: "all 0.2s",
        bgcolor: selected ? theme.palette.primary.light : "#f5f5f5",
        borderRadius: 2,
        aspectRatio: "1/1",
        "&:hover": {
          bgcolor: selected ? theme.palette.primary.light : "#e8e8e8",
          transform: "scale(1.05)",
          boxShadow: 2,
        },
        border: selected
          ? `2px solid ${theme.palette.primary.main}`
          : "1px solid #e0e0e0",
      }}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          onClick();
          e.preventDefault();
        }
      }}
    >
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: "100%",
          height: "100%",
        }}
      >
        {shape.icon}
      </Box>
    </Paper>
  );
};

export const Shapes = observer(() => {
  const store = React.useContext(StoreContext);
  const [activeTab, setActiveTab] =
    useState<keyof typeof SHAPE_CATEGORIES>("basic");
  const [selectedShape, setSelectedShape] = useState<ShapeType | null>(null);

  const handleTabChange = (
    event: React.SyntheticEvent,
    newValue: keyof typeof SHAPE_CATEGORIES
  ) => {
    setActiveTab(newValue);
  };

  const handleAddShape = (shapeType: ShapeType) => {
    store.addShapeElement(shapeType);
    setSelectedShape(shapeType);

    // 添加反馈效果
    setTimeout(() => {
      setSelectedShape(null);
    }, 500);
  };

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "background.paper",
        borderRadius: 1,
        boxShadow: 1,
      }}
    >
      <Box
        sx={{
          bgcolor: "grey.100",
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          flexShrink: 0,
          borderBottom: 1,
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1">图形</Typography>
      </Box>

      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        aria-label="形状分类"
        variant="fullWidth"
        sx={{ borderBottom: 1, borderColor: "divider" }}
      >
        {Object.entries(SHAPE_CATEGORIES).map(([key, label]) => (
          <Tab
            key={key}
            label={label}
            value={key}
            sx={{
              minHeight: "40px",
              typography: "body2",
              textTransform: "none",
            }}
          />
        ))}
      </Tabs>

      <Box
        sx={{
          bgcolor: "grey.100",
          flex: 1,
          overflow: "auto",
          p: 1.5,
          "&::-webkit-scrollbar": {
            width: "8px",
          },
        }}
      >
        {Object.keys(SHAPE_CATEGORIES).map((category) => (
          <Collapse in={activeTab === category} key={category} unmountOnExit>
            <Grid container spacing={1.5}>
              {SHAPE_RESOURCES.filter(
                (shape) => shape.category === category
              ).map((shape, index) => (
                <Grid item xs={4} key={shape.type}>
                  <ShapeItem
                    shape={shape}
                    selected={selectedShape === shape.type}
                    onClick={() => handleAddShape(shape.type)}
                  />
                </Grid>
              ))}
            </Grid>
          </Collapse>
        ))}
      </Box>
    </Box>
  );
});
